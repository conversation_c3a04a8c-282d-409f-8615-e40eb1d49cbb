import math


def calculate_sharpe_ratio(returns, risk_free_rate=0):
    """
    计算夏普比率

    Args:
        returns: 收益率列表
        risk_free_rate: 无风险收益率，默认为0

    Returns:
        夏普比率
    """
    if not returns or len(returns) < 2:
        return 0

    # 计算平均收益率
    mean_return = sum(returns) / len(returns)

    # 计算超额收益率
    excess_return = mean_return - risk_free_rate

    # 计算收益率的标准差
    variance = sum((r - mean_return) ** 2 for r in returns) / (len(returns) - 1)
    std_dev = math.sqrt(variance)

    # 避免除零错误
    if std_dev == 0:
        return 0

    # 计算夏普比率
    sharpe_ratio = excess_return / std_dev

    return sharpe_ratio